'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, Users, MessageCircle } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { chatApi, dataApi } from '@/services/chatService';



interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string | null;
  userRole: {
    id: number;
    name: string;
    isOwner: boolean;
    isAdmin: boolean;
    isMember: boolean;
  };
  isLeader?: boolean;
  organizationNames: string[];
  departmentNames: string[];
}

interface Organization {
  id: number;
  name: string;
  description?: string;
}

interface CreateChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChatCreated: (chat: any) => void;
}

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  width: 90vw;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
`;

const ModalHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
`;

const ModalTitle = styled.h2`
  margin: 0;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  color: ${appTheme.colors.text.primary};
  font-size: 18px;
`;

const CloseButton = styled.button`
  border: none;
  background: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};

  &:hover {
    background: ${appTheme.colors.background.lighter};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
  max-height: 60vh;
  overflow-y: auto;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  outline: none;
  margin-bottom: ${appTheme.spacing.md};

  &:focus {
    border-color: ${appTheme.colors.primary};
  }
`;

const ItemList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${appTheme.spacing.xs};
`;

const ItemCard = styled.div<{ $isSelected?: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.md};
  border: 1px solid
    ${props => (props.$isSelected ? appTheme.colors.primary : appTheme.colors.border)};
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  background: ${props =>
    props.$isSelected ? appTheme.colors.primaryLight : appTheme.colors.background.light};
  transition: all 0.2s ease;

  &:hover {
    border-color: ${appTheme.colors.primary};
    background: ${appTheme.colors.primaryLight};
  }
`;

const Avatar = styled.div<{ $src?: string }>`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: ${props => (props.$src ? `url(${props.$src})` : appTheme.colors.background.light)};
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${appTheme.colors.text.light};
  font-weight: 600;
  flex-shrink: 0;
`;

const ItemInfo = styled.div`
  flex: 1;
`;

const ItemName = styled.div`
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: 2px;
`;

const ItemDescription = styled.div`
  font-size: 12px;
  color: ${appTheme.colors.text.secondary};
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${appTheme.spacing.xs};
  margin-top: 4px;
`;

const RoleTag = styled.span<{ $roleType: 'owner' | 'admin' | 'member' | 'leader' }>`
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: ${appTheme.borderRadius.sm};
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  ${props => {
    switch (props.$roleType) {
      case 'owner':
        return `
          background: ${appTheme.colors.error.light};
          color: ${appTheme.colors.error.main};
          border: 1px solid ${appTheme.colors.error.main};
        `;
      case 'admin':
        return `
          background: #fef3c7;
          color: #d97706;
          border: 1px solid #d97706;
        `;
      case 'member':
        return `
          background: ${appTheme.colors.primary}20;
          color: ${appTheme.colors.primary};
          border: 1px solid ${appTheme.colors.primary};
        `;
      case 'leader':
        return `
          background: ${appTheme.colors.success.light};
          color: ${appTheme.colors.success.main};
          border: 1px solid ${appTheme.colors.success.main};
        `;
      default:
        return `
          background: ${appTheme.colors.background.lighter};
          color: ${appTheme.colors.text.secondary};
          border: 1px solid ${appTheme.colors.border};
        `;
    }
  }}
`;

const ContextInfo = styled.div`
  font-size: 11px;
  color: ${appTheme.colors.text.light};
  margin-top: 2px;
  line-height: 1.3;
`;

const ModalFooter = styled.div`
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.lg};
  border: 1px solid
    ${props => (props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.border)};
  border-radius: ${appTheme.borderRadius.md};
  background: ${props =>
    props.$variant === 'primary' ? appTheme.colors.primary : appTheme.colors.background.main};
  color: ${props =>
    props.$variant === 'primary' ? appTheme.colors.text.light : appTheme.colors.text.primary};
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    opacity: 0.9;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: ${appTheme.colors.error.main};
  font-size: 14px;
  margin-bottom: ${appTheme.spacing.md};
  padding: ${appTheme.spacing.sm};
  background: ${appTheme.colors.error.light};
  border-radius: ${appTheme.borderRadius.sm};
`;

const LoadingSpinner = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.xl};
  color: ${appTheme.colors.text.secondary};
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  margin-bottom: ${appTheme.spacing.md};
  opacity: 0.5;
`;

const EmptyStateText = styled.div`
  font-size: 14px;
`;

const FilterSection = styled.div`
  margin-bottom: ${appTheme.spacing.md};
`;

const FilterLabel = styled.label`
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.xs};
`;

const FilterSelect = styled.select`
  width: 100%;
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 14px;
  outline: none;
  background: ${appTheme.colors.background.main};
  color: ${appTheme.colors.text.primary};

  &:focus {
    border-color: ${appTheme.colors.primary};
  }
`;

const FilterGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${appTheme.spacing.md};
  margin-bottom: ${appTheme.spacing.md};
`;

export default function CreateChatModal({
  isOpen,
  onClose,
  onChatCreated,
}: CreateChatModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState('');
  const [currentUser, setCurrentUser] = useState<User | null>(null);



  // Filter states for private chat
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<number | null>(null);
  const [selectedDepartmentId, setSelectedDepartmentId] = useState<number | null>(null);
  const [availableOrganizations, setAvailableOrganizations] = useState<Organization[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<Organization[]>([]);

  // Data states
  const [users, setUsers] = useState<User[]>([]);

  // Load current user info
  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        const response = await dataApi.getCurrentUser();
        console.log('Current user response:', response);
        setCurrentUser(response.user);
      } catch (error) {
        console.error('Failed to load current user:', error);
      }
    };

    if (isOpen) {
      loadCurrentUser();
    }
  }, [isOpen]);

  // Load data for private chat using role-based API
  useEffect(() => {
    const loadData = async () => {
      if (!isOpen || !currentUser) return;

      setLoading(true);
      setError('');

      try {
        // Load organizations based on user role
        const orgsResponse = await dataApi.getChatModalOrganizations();
        console.log('Organizations response:', orgsResponse);
        setAvailableOrganizations(orgsResponse.organizations || []);

        // Set default to first organization if available
        if (orgsResponse.organizations?.length > 0) {
          setSelectedOrganizationId(orgsResponse.organizations[0].id);
          // Load users from first organization by default
          const usersResponse = await dataApi.getChatModalUsers(orgsResponse.organizations[0].id);
          console.log('Users response:', usersResponse);
          setUsers(usersResponse.users || []);
        }
      } catch (error) {
        setError('Failed to load private chat data');
        console.error('Failed to load data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [isOpen, currentUser]);

  // Handle filter changes for private chat using role-based API
  useEffect(() => {
    const loadFilteredData = async () => {
      if (!currentUser || !selectedOrganizationId) return;

      setLoading(true);
      setError('');

      try {
        // Load users based on organization and department filters using role-based API
        const usersResponse = await dataApi.getChatModalUsers(
          selectedOrganizationId,
          selectedDepartmentId || undefined
        );
        console.log('Filtered users response:', usersResponse);
        setUsers(usersResponse.users || []);
        setSelectedItems([]); // Clear selections when filters change
      } catch (error) {
        console.error('Failed to load filtered users:', error);
        setError('Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    if (selectedOrganizationId !== null) {
      loadFilteredData();
    }
  }, [selectedOrganizationId, selectedDepartmentId, currentUser]);

  // Load departments when organization changes using role-based API
  useEffect(() => {
    const loadOrganizationDepartments = async () => {
      if (!selectedOrganizationId) {
        setAvailableDepartments([]);
        return;
      }

      try {
        const deptsResponse = await dataApi.getChatModalDepartments(selectedOrganizationId);
        console.log('Departments response:', deptsResponse);
        setAvailableDepartments(deptsResponse.departments || []);
      } catch (error) {
        console.error('Failed to load organization departments:', error);
        setAvailableDepartments([]);
      }
    };

    loadOrganizationDepartments();
  }, [selectedOrganizationId]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSearchQuery('');
      setSelectedItems([]);
      setError('');
      setSelectedOrganizationId(null);
      setSelectedDepartmentId(null);
    }
  }, [isOpen]);

  const handleItemSelect = (id: number) => {
    // For private chats, allow multiple selection
    setSelectedItems(prev =>
      prev.includes(id) ? prev.filter(item => item !== id) : [...prev, id]
    );
  };

  const getFilteredItems = () => {
    const query = searchQuery.toLowerCase();

    return users.filter(user => {
      const fullName = `${user.firstName || ''} ${user.lastName || ''}`.toLowerCase();
      const email = (user.email || '').toLowerCase();
      return fullName.includes(query) || email.includes(query);
    });
  };

  const handleCreateChat = async () => {
    // Check if selection is required
    if (selectedItems.length === 0) {
      setError('Please select at least one user');
      return;
    }

    setCreating(true);
    setError('');

    try {
      const chatData: any = {
        chatType: 'PRIVATE',
        participantIds: selectedItems,
        name: users
          .filter(user => selectedItems.includes(user.id))
          .map(user => `${user.firstName} ${user.lastName}`)
          .join(', '),
      };

      // Check if private chat with these users already exists
      const existingChat = await chatApi.checkChatExists({
        chatType: 'private',
        participantIds: [currentUser!.id, ...selectedItems],
      });

      if (existingChat) {
        setError('Private chat already exists');
        return;
      }

      const response = await chatApi.createChat(chatData);
      onChatCreated(response.chat);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to create chat');
    } finally {
      setCreating(false);
    }
  };

  const getModalTitle = () => {
    return (
      <>
        <MessageCircle size={20} />
        Create Private Chat
      </>
    );
  };

  const renderItems = () => {
    const items = getFilteredItems();

    const getRoleTags = (user: User) => {
      const tags = [];

      if (user.userRole?.isOwner) {
        tags.push(<RoleTag key="owner" $roleType="owner">Owner</RoleTag>);
      } else if (user.userRole?.isAdmin) {
        tags.push(<RoleTag key="admin" $roleType="admin">Admin</RoleTag>);
      } else if (user.userRole?.isMember) {
        tags.push(<RoleTag key="member" $roleType="member">Member</RoleTag>);
      }

      if (user.isLeader) {
        tags.push(<RoleTag key="leader" $roleType="leader">Leader</RoleTag>);
      }

      return tags;
    };

    const getContextInfo = (user: User) => {
      const parts = [];

      if (user.organizationNames?.length > 0) {
        parts.push(`Org: ${user.organizationNames.join(', ')}`);
      }

      if (user.departmentNames?.length > 0) {
        parts.push(`Dept: ${user.departmentNames.join(', ')}`);
      }

      return parts.join(' • ');
    };

    return items.map((user: User) => (
      <ItemCard
        key={user.id}
        $isSelected={selectedItems.includes(user.id)}
        onClick={() => handleItemSelect(user.id)}
      >
        <Avatar $src={user.imageUrl || undefined}>
          {!user.imageUrl &&
            `${(user.firstName || '')[0] || ''}${(user.lastName || '')[0] || ''}`}
        </Avatar>
        <ItemInfo>
          <ItemName>
            {`${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User'}
          </ItemName>
          <ItemDescription>
            {user.email || 'No email'}
          </ItemDescription>
          <TagsContainer>
            {getRoleTags(user)}
          </TagsContainer>
          {getContextInfo(user) && (
            <ContextInfo>
              {getContextInfo(user)}
            </ContextInfo>
          )}
        </ItemInfo>
      </ItemCard>
    ));
  };

  return (
    <ModalOverlay $isOpen={isOpen} onClick={onClose}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>{getModalTitle()}</ModalTitle>
          <CloseButton onClick={onClose}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          {error && <ErrorMessage>{error}</ErrorMessage>}

          <FilterSection>
            <FilterGrid>
              <div>
                <FilterLabel>Organization</FilterLabel>
                <FilterSelect
                  value={selectedOrganizationId || ''}
                  onChange={e => {
                    const orgId = e.target.value ? parseInt(e.target.value) : null;
                    setSelectedOrganizationId(orgId);
                    setSelectedDepartmentId(null); // Reset department when org changes
                  }}
                >
                  <option value="">Select Organization</option>
                  {availableOrganizations.map(org => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </FilterSelect>
              </div>
              <div>
                <FilterLabel>Department (Optional)</FilterLabel>
                <FilterSelect
                  value={selectedDepartmentId || ''}
                  onChange={e => {
                    const deptId = e.target.value ? parseInt(e.target.value) : null;
                    setSelectedDepartmentId(deptId);
                  }}
                  disabled={!selectedOrganizationId}
                >
                  <option value="">All Departments</option>
                  {availableDepartments.map(dept => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name}
                    </option>
                  ))}
                </FilterSelect>
              </div>
            </FilterGrid>
          </FilterSection>

          <SearchInput
            type="text"
            placeholder="Search members..."
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
          />

          {loading ? (
            <LoadingSpinner>Loading...</LoadingSpinner>
          ) : (
            <ItemList>
              {renderItems()}
              {getFilteredItems().length === 0 ? (
                <EmptyState>
                  <EmptyStateIcon>
                    <Users size={40} />
                  </EmptyStateIcon>
                  <EmptyStateText>
                    No members found.
                  </EmptyStateText>
                </EmptyState>
              ) : null}
            </ItemList>
          )}
        </ModalBody>

        <ModalFooter>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            $variant="primary"
            onClick={handleCreateChat}
            disabled={creating || selectedItems.length === 0}
          >
            {creating ? 'Creating...' : 'Create Chat'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
