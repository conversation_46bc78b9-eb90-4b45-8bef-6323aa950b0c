import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { hasOrganizationAdminPrivileges } from '@/lib/permissions';

/**
 * GET API for fetching users/members based on organization and department for chat modal
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization
 * - departmentId: Optional. ID of the department to filter users
 * 
 * Role-based access:
 * - Owner: Can see all users in organizations they own
 * - Admin: Can see all users in organizations where they have admin privileges
 * - Member: Can see all users in organizations where they are a member
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgId = parseInt(organizationId);
    if (isNaN(orgId)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    let deptId: number | null = null;
    if (departmentId) {
      deptId = parseInt(departmentId);
      if (isNaN(deptId)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }
    }

    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: orgId },
      select: { id: true, ownerUserId: true },
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Verify access to organization
    let hasAccess = false;

    if (auth.isOwner) {
      hasAccess = organization.ownerUserId === auth.userId;
    } else if (auth.isAdmin) {
      hasAccess = await hasOrganizationAdminPrivileges(auth.userId, orgId);
    } else if (auth.isMember) {
      const membershipCount = await prisma.departmentMember.count({
        where: {
          userId: auth.userId,
          department: {
            organizationId: orgId,
          },
        },
      });
      hasAccess = membershipCount > 0;
    }

    if (!hasAccess) {
      return NextResponse.json({ error: 'You do not have access to this organization' }, { status: 403 });
    }

    // If department is specified, verify access to department
    if (deptId) {
      const department = await prisma.department.findUnique({
        where: { id: deptId },
        select: { organizationId: true },
      });

      if (!department || department.organizationId !== orgId) {
        return NextResponse.json({ error: 'Department not found or does not belong to organization' }, { status: 404 });
      }

      // For members, verify they have access to the specific department
      if (auth.isMember) {
        const deptMembershipCount = await prisma.departmentMember.count({
          where: {
            userId: auth.userId,
            departmentId: deptId,
          },
        });

        if (deptMembershipCount === 0) {
          return NextResponse.json({ error: 'You do not have access to this department' }, { status: 403 });
        }
      }
    }

    // Fetch users based on role and filters
    let users: any[] = [];

    if (deptId) {
      // Get users from specific department
      const departmentMembers = await prisma.departmentMember.findMany({
        where: {
          departmentId: deptId,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                  isOwner: true,
                  isAdmin: true,
                  isMember: true,
                },
              },
            },
          },
        },
      });

      users = departmentMembers
        .map(member => member.user)
        .filter(user => user.id !== auth.userId) // Exclude current user
        .sort((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`));
    } else {
      // Get all users from organization
      if (auth.isOwner || auth.isAdmin) {
        // Owners and admins can see all users in the organization
        const organizationMembers = await prisma.departmentMember.findMany({
          where: {
            department: {
              organizationId: orgId,
            },
          },
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                imageUrl: true,
                userRole: {
                  select: {
                    id: true,
                    name: true,
                    isOwner: true,
                    isAdmin: true,
                    isMember: true,
                  },
                },
              },
            },
          },
        });

        // Remove duplicates and current user
        const userMap = new Map();
        organizationMembers.forEach(member => {
          if (member.user.id !== auth.userId && !userMap.has(member.user.id)) {
            userMap.set(member.user.id, member.user);
          }
        });

        users = Array.from(userMap.values())
          .sort((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`));
      } else if (auth.isMember) {
        // Members can see users from departments they are also a member of
        const memberDepartments = await prisma.departmentMember.findMany({
          where: {
            userId: auth.userId,
            department: {
              organizationId: orgId,
            },
          },
          select: { departmentId: true },
        });

        const departmentIds = memberDepartments.map(member => member.departmentId);

        if (departmentIds.length > 0) {
          const organizationMembers = await prisma.departmentMember.findMany({
            where: {
              departmentId: { in: departmentIds },
            },
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                  imageUrl: true,
                  userRole: {
                    select: {
                      id: true,
                      name: true,
                      isOwner: true,
                      isAdmin: true,
                      isMember: true,
                    },
                  },
                },
              },
            },
          });

          // Remove duplicates and current user
          const userMap = new Map();
          organizationMembers.forEach(member => {
            if (member.user.id !== auth.userId && !userMap.has(member.user.id)) {
              userMap.set(member.user.id, member.user);
            }
          });

          users = Array.from(userMap.values())
            .sort((a, b) => `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`));
        }
      }
    }

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users for chat modal:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}
