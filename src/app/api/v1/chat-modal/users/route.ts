import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { authenticateUser } from '@/lib/auth';
import { hasOrganizationAdminPrivileges } from '@/lib/permissions';

/**
 * Enhanced user interface with additional context information
 */
interface EnhancedUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string;
  userRole: {
    id: number;
    name: string;
    isOwner: boolean;
    isAdmin: boolean;
    isMember: boolean;
  };
  isLeader?: boolean;
  organizationNames: string[];
  departmentNames: string[];
}

/**
 * Get enhanced user information from a specific department
 */
async function getEnhancedUsersFromDepartment(
  departmentId: number,
  currentUserId: number,
  organizationId: number
): Promise<EnhancedUser[]> {
  // Get all users from the department (members)
  const departmentMembers = await prisma.departmentMember.findMany({
    where: { departmentId },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
          userRole: {
            select: {
              id: true,
              name: true,
              isOwner: true,
              isAdmin: true,
              isMember: true,
            },
          },
        },
      },
      department: {
        select: {
          name: true,
          organization: {
            select: { name: true },
          },
        },
      },
    },
  });

  // Get organization owner
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
    select: {
      name: true,
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
          userRole: {
            select: {
              id: true,
              name: true,
              isOwner: true,
              isAdmin: true,
              isMember: true,
            },
          },
        },
      },
    },
  });

  // Get organization admins
  const orgAdmins = await prisma.organizationAdmin.findMany({
    where: { organizationId, isActive: true },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
          userRole: {
            select: {
              id: true,
              name: true,
              isOwner: true,
              isAdmin: true,
              isMember: true,
            },
          },
        },
      },
    },
  });

  const enhancedUsers: EnhancedUser[] = [];
  const processedUserIds = new Set<number>();

  // Add organization owner if not current user
  if (organization?.owner && organization.owner.id !== currentUserId) {
    enhancedUsers.push({
      ...organization.owner,
      isLeader: false,
      organizationNames: [organization.name],
      departmentNames: [],
    });
    processedUserIds.add(organization.owner.id);
  }

  // Add organization admins if not current user and not already processed
  orgAdmins.forEach(admin => {
    if (admin.user.id !== currentUserId && !processedUserIds.has(admin.user.id)) {
      enhancedUsers.push({
        ...admin.user,
        isLeader: false,
        organizationNames: [organization?.name || ''],
        departmentNames: [],
      });
      processedUserIds.add(admin.user.id);
    }
  });

  // Add department members if not current user and not already processed
  departmentMembers.forEach(member => {
    if (member.user.id !== currentUserId && !processedUserIds.has(member.user.id)) {
      enhancedUsers.push({
        ...member.user,
        isLeader: member.isLeader,
        organizationNames: [member.department.organization.name],
        departmentNames: [member.department.name],
      });
      processedUserIds.add(member.user.id);
    }
  });

  return enhancedUsers.sort((a, b) =>
    `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
  );
}

/**
 * Get enhanced user information from an organization
 */
async function getEnhancedUsersFromOrganization(
  organizationId: number,
  currentUserId: number,
  isOwner: boolean,
  isAdmin: boolean,
  isMember: boolean
): Promise<EnhancedUser[]> {
  const enhancedUsers: EnhancedUser[] = [];
  const processedUserIds = new Set<number>();

  // Get organization details
  const organization = await prisma.organization.findUnique({
    where: { id: organizationId },
    select: { name: true, ownerUserId: true },
  });

  if (!organization) return [];

  // Get organization owner
  const owner = await prisma.organization.findUnique({
    where: { id: organizationId },
    include: {
      owner: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
          userRole: {
            select: {
              id: true,
              name: true,
              isOwner: true,
              isAdmin: true,
              isMember: true,
            },
          },
        },
      },
    },
  });

  // Add organization owner if not current user
  if (owner?.owner && owner.owner.id !== currentUserId) {
    enhancedUsers.push({
      ...owner.owner,
      isLeader: false,
      organizationNames: [organization.name],
      departmentNames: [],
    });
    processedUserIds.add(owner.owner.id);
  }

  // Get organization admins
  const orgAdmins = await prisma.organizationAdmin.findMany({
    where: { organizationId, isActive: true },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          imageUrl: true,
          userRole: {
            select: {
              id: true,
              name: true,
              isOwner: true,
              isAdmin: true,
              isMember: true,
            },
          },
        },
      },
    },
  });

  // Add organization admins if not current user and not already processed
  orgAdmins.forEach(admin => {
    if (admin.user.id !== currentUserId && !processedUserIds.has(admin.user.id)) {
      enhancedUsers.push({
        ...admin.user,
        isLeader: false,
        organizationNames: [organization.name],
        departmentNames: [],
      });
      processedUserIds.add(admin.user.id);
    }
  });

  // Get department members based on user role
  let departmentMembers: any[] = [];

  if (isOwner || isAdmin) {
    // Owners and admins can see all members in the organization
    departmentMembers = await prisma.departmentMember.findMany({
      where: {
        department: { organizationId },
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            imageUrl: true,
            userRole: {
              select: {
                id: true,
                name: true,
                isOwner: true,
                isAdmin: true,
                isMember: true,
              },
            },
          },
        },
        department: {
          select: { name: true },
        },
      },
    });
  } else if (isMember) {
    // Members can see users from departments they are also a member of
    const memberDepartments = await prisma.departmentMember.findMany({
      where: {
        userId: currentUserId,
        department: { organizationId },
      },
      select: { departmentId: true },
    });

    const departmentIds = memberDepartments.map(member => member.departmentId);

    if (departmentIds.length > 0) {
      departmentMembers = await prisma.departmentMember.findMany({
        where: {
          departmentId: { in: departmentIds },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              imageUrl: true,
              userRole: {
                select: {
                  id: true,
                  name: true,
                  isOwner: true,
                  isAdmin: true,
                  isMember: true,
                },
              },
            },
          },
          department: {
            select: { name: true },
          },
        },
      });
    }
  }

  // Process department members and build enhanced user objects
  const userDepartmentMap = new Map<number, string[]>();

  departmentMembers.forEach(member => {
    const userId = member.user.id;
    if (!userDepartmentMap.has(userId)) {
      userDepartmentMap.set(userId, []);
    }
    userDepartmentMap.get(userId)!.push(member.department.name);

    if (userId !== currentUserId && !processedUserIds.has(userId)) {
      enhancedUsers.push({
        ...member.user,
        isLeader: member.isLeader,
        organizationNames: [organization.name],
        departmentNames: [member.department.name],
      });
      processedUserIds.add(userId);
    } else if (userId !== currentUserId && processedUserIds.has(userId)) {
      // Update existing user's department names
      const existingUser = enhancedUsers.find(u => u.id === userId);
      if (existingUser && !existingUser.departmentNames.includes(member.department.name)) {
        existingUser.departmentNames.push(member.department.name);
        // Update leadership status if user is a leader in any department
        if (member.isLeader) {
          existingUser.isLeader = true;
        }
      }
    }
  });

  return enhancedUsers.sort((a, b) =>
    `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`)
  );
}

/**
 * GET API for fetching users/members based on organization and department for chat modal
 * 
 * Query parameters:
 * - organizationId: Required. ID of the organization
 * - departmentId: Optional. ID of the department to filter users
 * 
 * Role-based access:
 * - Owner: Can see all users in organizations they own
 * - Admin: Can see all users in organizations where they have admin privileges
 * - Member: Can see all users in organizations where they are a member
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateUser(request);
    if ('error' in auth) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId');
    const departmentId = url.searchParams.get('departmentId');

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    const orgId = parseInt(organizationId);
    if (isNaN(orgId)) {
      return NextResponse.json({ error: 'Invalid organization ID' }, { status: 400 });
    }

    let deptId: number | null = null;
    if (departmentId) {
      deptId = parseInt(departmentId);
      if (isNaN(deptId)) {
        return NextResponse.json({ error: 'Invalid department ID' }, { status: 400 });
      }
    }

    // Check if organization exists
    const organization = await prisma.organization.findUnique({
      where: { id: orgId },
      select: { id: true, ownerUserId: true },
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    // Verify access to organization
    let hasAccess = false;

    if (auth.isOwner) {
      hasAccess = organization.ownerUserId === auth.userId;
    } else if (auth.isAdmin) {
      hasAccess = await hasOrganizationAdminPrivileges(auth.userId, orgId);
    } else if (auth.isMember) {
      const membershipCount = await prisma.departmentMember.count({
        where: {
          userId: auth.userId,
          department: {
            organizationId: orgId,
          },
        },
      });
      hasAccess = membershipCount > 0;
    }

    if (!hasAccess) {
      return NextResponse.json({ error: 'You do not have access to this organization' }, { status: 403 });
    }

    // If department is specified, verify access to department
    if (deptId) {
      const department = await prisma.department.findUnique({
        where: { id: deptId },
        select: { organizationId: true },
      });

      if (!department || department.organizationId !== orgId) {
        return NextResponse.json({ error: 'Department not found or does not belong to organization' }, { status: 404 });
      }

      // For members, verify they have access to the specific department
      if (auth.isMember) {
        const deptMembershipCount = await prisma.departmentMember.count({
          where: {
            userId: auth.userId,
            departmentId: deptId,
          },
        });

        if (deptMembershipCount === 0) {
          return NextResponse.json({ error: 'You do not have access to this department' }, { status: 403 });
        }
      }
    }

    // Fetch users based on role and filters with enhanced information
    let users: any[] = [];

    if (deptId) {
      // Get users from specific department with enhanced information
      users = await getEnhancedUsersFromDepartment(deptId, auth.userId, orgId);
    } else {
      // Get all users from organization with enhanced information
      users = await getEnhancedUsersFromOrganization(orgId, auth.userId, auth.isOwner, auth.isAdmin, auth.isMember);
    }

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users for chat modal:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}
